<script setup lang="ts">

import { formatDate } from '@lib/scripts/utils'
import { Guard, guard } from '@/plugins/guard'
import { computed, ref } from 'vue'
import { backorderRulesList } from '@/modules/orders/store'
import { facilitiesList, handleDetailsError } from '@/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/button/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import DetailsCard from '@/components/DetailsCard.vue'
import BillingDetails from '@/modules/orders/views/live-details/components/BillingDetails.vue'
import ContentSwitcher from '@lib/components/blocks/content-switcher/ContentSwitcher.vue'
import ShippingDetails from '@/modules/orders/views/live-details/components/ShippingDetails.vue'
import EditDetailsForm from '@/modules/orders/views/draft-details/components/EditDetailsForm.vue'
import EditOrderDetailsForm from '@/modules/orders/views/draft-details/components/EditOrderDetailsForm.vue'

import type { ImportError } from '@/types'
import type { DraftOrder, OrderBilling, OrderShipping } from '@/modules/orders/types'

const props = defineProps<{
  order?:         DraftOrder
  editable?:      boolean
  updatePending?: boolean
}>()

defineEmits<{
  ( eventName: 'updateOrder' ): void
  ( eventName: 'openActions' ): void
}>()

const toggleInfo                = ref( 1 )
const editDetails               = ref<boolean>( false )
const editOrderDetails          = ref( false )
const billingImportErrors       = computed(() => JSON.parse( JSON.stringify( props.order?.importErrors )) as ImportError<OrderBilling>[] )
const shippingImportErrors      = computed(() => JSON.parse( JSON.stringify( props.order?.importErrors )) as ImportError<OrderShipping>[] )
const updateOrderDetailsPending = ref( false )

function getDate() {
  const dateTimeCreated = formatDate( props.order.createdTs, 'MMM DD, YYYY [at] HH:mm' )
  const orderType = props?.order?.source ? ` via ${props.order.source}` : ''
  return dateTimeCreated + orderType
}

const infoOptions: DropListOption[] = [
  {
    id:   1,
    name: 'Ship To',
  },
  {
    id:   2,
    name: 'Bill To',
  }

]

</script>

<template>

  <div class="w-full h-full grid grid-rows-1 md:grid-rows-[1fr_max-content] bg-core-10 border-t lg:border-t-0 border-core-30 overflow-hidden">

    <div class="w-full h-full overflow-hidden overflow-y-auto">

      <div class="w-full h-10 sticky top-0 z-1 pl-4 flex items-center space-x-3 bg-core-20 border-b border-core-30">

        <Icon name="details" size="m" class="text-main" />

        <p class="text-sm font-medium grow">
          Details
        </p>

        <Guard scope="Order.Write">

          <Button
            v-if="editable"
            mode="ghost"
            size="auto"
            class="h-full px-4 gap-x-3"
            :disabled="!editable"
            @click="editOrderDetails = true"
          >

            <p class="text-sm">
              Edit
            </p>

            <Icon name="edit" size="s" />

          </Button>

        </Guard>

      </div>

      <div v-if="order" class="text-sm grid grid-cols-2 gap-px bg-core-30 border-b border-core-30">

        <DetailsCard
          label="Client Reference Number"
          class="lg:col-span-2"
          :class="{
            'col-span-2': !order?.masterRecordId,
          }"
          :content="order.clientReference"
          :error="handleDetailsError('clientReference', order.importErrors)"
          :update-pending="updatePending"
        />

        <DetailsCard
          v-if="order?.masterRecordId"
          :to="order?.masterRecordId ? { name: 'Live Order Details', params: { orderId: order?.masterRecordId } } : null"
          label="Order ID"
          class="lg:hidden"
          :content="order?.masterRecordId ? String(order?.masterRecordId) : '/'"
          :error="handleDetailsError('masterRecordId', order.importErrors)"
          :update-pending="updatePending"
        />

        <DetailsCard
          label="Group Name"
          class="col-span-2"
          :content="order.groupName || '/'"
          :error="handleDetailsError('groupName', order.importErrors)"
          :update-pending="updatePending"
        />

        <DetailsCard
          label="Date Created"
          class="col-span-2"
          :content="order.createdTs ? getDate() : new Date().toString()"
          :error="handleDetailsError('createdTs', order.importErrors)"
          :update-pending="updatePending"
        />

        <DetailsCard
          label="Facility"
          :content="order.facilityCode ? facilitiesList?.find(f => f.id === order.facilityCode)?.name : '/'"
          :error="handleDetailsError('facilityCode', order.importErrors)"
          :update-pending="updatePending"
        />

        <DetailsCard
          label="Type"
          :content="order.isBusinessOrder ? 'Business' : 'Consumer'"
          :error="handleDetailsError('isBusinessOrder', order.importErrors)"
          :update-pending="updatePending"
        />

        <DetailsCard
          label="PO Number"
          :content="order.poNumber || '/'"
          :error="handleDetailsError('poNumber', order.importErrors)"
          :update-pending="updatePending"
        />

        <DetailsCard
          label="Use Gift Invoice"
          :content="order.isGift ? 'Yes' : 'No'"
          :error="handleDetailsError('isGift', order.importErrors)"
          :update-pending="updatePending"
        />

        <DetailsCard
          v-if="order.isGift"
          label="Gift Message"
          class="col-span-2"
          :content="order.giftMessage || '/'"
          :error="handleDetailsError('giftMessage', order.importErrors)"
          :update-pending="updatePending"
        />

        <DetailsCard
          label="Backorder Rule"
          class="col-span-2"
          :content="backorderRulesList.find(r => r.id === order.backorderRule)?.name || '/'"
          :error="handleDetailsError('backorderRule', order.importErrors)"
          :update-pending="updatePending"
        />

      </div>

      <div
        class="w-full h-10 sticky top-0 z-1 pl-4 flex space-x-3 bg-core-20 border-b border-core-30"
        :class="{
          'justify-between': guard('Order.Write'),
          'justify-center': !guard('Order.Write'),
        }"
      >

        <div class="h-full flex items-center justify-center">

          <ContentSwitcher v-model="toggleInfo" :items="infoOptions" />

        </div>

        <Guard scope="Order.Write">

          <Button
            v-if="editable"
            mode="ghost"
            size="auto"
            class="h-full px-4 gap-x-3"
            :disabled="!editable"
            @click="editDetails = true"
          >

            <p class="text-sm">
              Edit
            </p>

            <Icon name="edit" size="s" />

          </Button>

        </Guard>

      </div>

      <Transition v-if="order" name="source-form" mode="out-in">

        <ShippingDetails
          v-if="toggleInfo === 1"
          :shipping="order?.shipping"
          :import-errors="shippingImportErrors"
          :update-pending="updatePending"
        />

        <BillingDetails
          v-else
          :billing="order?.billing"
          :import-errors="billingImportErrors"
          :update-pending="updatePending"
        />

      </Transition>

      <!-- Edit Details Form -->

      <Sidebar
        :dim="true"
        :open="editOrderDetails"
        :strict="true"
        @close="editOrderDetails = false"
      >

        <div class="w-full h-full grid grid-rows-[max-content_1fr]">

          <div class="w-full h-12 sticky top-0 z-1 flex items-center bg-core-20 border-b border-core-30">

            <div class="h-full px-4 flex items-center space-x-3 grow border-r border-core-30">

              <Icon name="edit" size="s" class="text-main" />

              <p class="text-sm font-medium">
                Edit Order Details <span class="text-main">[{{ order?.id }}]</span>
              </p>

            </div>

            <Button
              type="box"
              size="auto"
              mode="ghost"
              icon="close"
              class="w-12 h-full min-w-[3rem]"
              :disabled="updateOrderDetailsPending"
              @click="editOrderDetails = false"
            />

          </div>

          <EditOrderDetailsForm
            v-model:pending="updateOrderDetailsPending"
            :order
            @update="() => {
              editOrderDetails = false
              $emit('updateOrder')
            }"
            @close="editOrderDetails = false"
          />

        </div>

      </Sidebar>

      <Sidebar
        :dim="true"
        :open="editDetails"
        :strict="true"
        @close="editDetails = false"
      >

        <div class="w-full h-full grid grid-rows-[max-content_1fr]">

          <div class="w-full h-12 sticky top-0 z-1 flex items-center bg-core-20 border-b border-core-30">

            <div class="h-full px-4 flex items-center space-x-3 grow border-r border-core-30">

              <Icon name="edit" size="s" class="text-main" />

              <p class="text-sm font-medium">
                Edit Details <span class="text-main">[{{ order?.id }}]</span>
              </p>

            </div>

            <Button
              type="box"
              size="auto"
              mode="ghost"
              icon="close"
              class="w-12 h-full min-w-[3rem]"
              :disabled="updateOrderDetailsPending"
              @click="editDetails = false"
            />

          </div>

          <EditDetailsForm
            v-model:pending="updateOrderDetailsPending"
            :order
            @update="() => {
              editDetails = false
              $emit('updateOrder')
            }"
            @close="editDetails = false"
          />

        </div>

      </Sidebar>

    </div>

  </div>

</template>
