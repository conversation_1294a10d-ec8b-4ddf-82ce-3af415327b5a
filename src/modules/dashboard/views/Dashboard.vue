<script setup lang="ts">

import { onMounted, ref } from 'vue'
import { getOrdersByStatus } from '@/modules/orders/store'
import { setAlertOptions, setNotificationOptions } from '@lib/store/snackbar'

import KpiPanel from '@/modules/dashboard/components/KpiPanel.vue'
import GlobePanel from '@/modules/dashboard/components/GlobePanel.vue'
import StatusPanel from '@/modules/dashboard/components/StatusPanel.vue'
import Button from '@lib/components/button/Button.vue'

import type { OrdersByStatusPayload } from '@/modules/orders/types'

const ordersByStatus = ref<OrdersByStatusPayload>({
  onHold:                   [],
  voided:                   [],
  shipped:                  [],
  unknown:                  [],
  backordered:              [],
  atWarehouse:              [],
  totalNumberOrders:        0,
  shippingMethodKeys:       [],
  fulfillmentStatusPercent: 0
})

onMounted( async () => {

  const { payload } = await getOrdersByStatus()

  ordersByStatus.value = payload

})

// Test functions for snackbar notifications and alerts
function showInfoAlert() {
  setAlertOptions({
    message:  'Information Alert',
    details:  'This is an informational alert message with additional details.',
    severity: 'info'
  })
}

function showSuccessAlert() {
  setAlertOptions({
    message:  'Success Alert',
    details:  'Operation completed successfully!',
    severity: 'success'
  })
}

function showWarningAlert() {
  setAlertOptions({
    message:  'Warning Alert',
    details:  'Please review this warning message carefully.',
    severity: 'warning'
  })
}

function showErrorAlert() {
  setAlertOptions({
    message:  'Error Alerttttttttt',
    details:  'An error occurred while processing your request.',
    severity: 'error',
    strict:   true
  })
}

function showAlertWithAction() {
  setAlertOptions({
    message:    'Alert with Action',
    details:    'This alert includes an action button.',
    severity:   'warning',
    actionName: 'Retry',
    action:     () => {
      console.log( 'Action button clicked!' )
      showSuccessNotification()
    }
  })
}

function showAlertWithInlineAction() {
  setAlertOptions({
    message:          'Alert with Inline Action',
    details:          'This alert has an inline action link.',
    severity:         'info',
    inlineActionName: 'Learn More',
    inlineAction:     () => {
      console.log( 'Inline action clicked!' )
      showInfoNotification()
    }
  })
}

function showInfoNotification() {
  setNotificationOptions({
    message:  'Info Notification',
    details:  'This is an informational notification.',
    severity: 'info'
  })
}

function showSuccessNotification() {
  setNotificationOptions({
    message:  'Success Notification',
    details:  'Task completed successfully!',
    severity: 'success'
  })
}

function showNotificationWithAction() {
  setNotificationOptions({
    message:    'Notification with Action',
    details:    'This notification includes an action.',
    severity:   'info',
    actionName: 'View Details',
    action:     () => {
      console.log( 'Notification action clicked!' )
      showSuccessAlert()
    }
  })
}

</script>

<template>

  <div class="h-full relative lg:grid lg:grid-rows-[max-content_1fr] lg:grid-cols-2 2xl:grid-cols-[1fr_max-content] bg-layer-02 overflow-hidden overflow-y-auto lg:overflow-y-hidden xl:overflow-y-hidden">

    <StatusPanel :orders-by-status="ordersByStatus" class="lg:bg-layer-01 absolute top-0 left-0 z-1 lg:relative lg:col-span-2 xl:col-span-1 lg:border-b border-border-subtle-00" />

    <GlobePanel class="xl:w-full 2xl:w-[40rem] lg:min-h-full h-[80%] lg:row-start-2 xl:row-span-2 lg:col-start-2 xl:col-start-auto relative" />

    <KpiPanel :orders-by-status="ordersByStatus" class="lg:col-start-1 lg:row-span-1" />

    <!-- Snackbar Test Buttons Section -->
    <div class="lg:col-span-2 xl:col-span-1 p-4 bg-layer-01 border-t border-border-subtle-00">
      <h3 class="text-lg font-medium text-text-primary mb-4">
        Snackbar Test Buttons
      </h3>

      <!-- Alert Buttons -->
      <div class="mb-6">
        <h4 class="text-sm font-medium text-text-secondary mb-3">
          Alerts
        </h4>
        <div class="flex flex-wrap gap-2">
          <Button
            size="m"
            mode="tertiary"
            modifier="neutral"
            @click="showInfoAlert"
          >
            Info Alert
          </Button>

          <Button
            size="m"
            mode="tertiary"
            modifier="success"
            @click="showSuccessAlert"
          >
            Success Alert
          </Button>

          <Button
            size="m"
            mode="tertiary"
            @click="showWarningAlert"
          >
            Warning Alert
          </Button>

          <Button
            size="m"
            mode="tertiary"
            modifier="danger"
            @click="showErrorAlert"
          >
            Error Alert
          </Button>

          <Button
            size="m"
            mode="secondary"
            @click="showAlertWithAction"
          >
            Alert with Action
          </Button>

          <Button
            size="m"
            mode="ghost"
            @click="showAlertWithInlineAction"
          >
            Alert with Inline Action
          </Button>
        </div>
      </div>

      <!-- Notification Buttons -->
      <div>
        <h4 class="text-sm font-medium text-text-secondary mb-3">
          Notifications
        </h4>
        <div class="flex flex-wrap gap-2">
          <Button
            size="m"
            mode="primary"
            @click="showInfoNotification"
          >
            Info Notification
          </Button>

          <Button
            size="m"
            mode="primary"
            modifier="success"
            @click="showSuccessNotification"
          >
            Success Notification
          </Button>

          <Button
            size="m"
            mode="secondary"
            @click="showNotificationWithAction"
          >
            Notification with Action
          </Button>
        </div>
      </div>
    </div>

  </div>

</template>

<style>
.cds--cc--gauge path.arc-background {
  fill: var(--border-subtle-00) !important;
}
</style>
