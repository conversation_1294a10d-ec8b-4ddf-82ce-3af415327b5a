.default-design-system {

  .button {

    @apply h-auto relative px-4 flex items-center cursor-pointer;

    &.box, &.badge {
      @apply px-0 justify-center;
    }

    &:has(.button-icon) {
      @apply justify-center;
    }

    &.xs {

      @apply text-xs h-[1.625rem] min-h-[1.625rem];

      &.box {
        @apply w-[1.625rem] min-w-[1.625rem];
      }

      &.badge {
        @apply w-[1.625rem] min-w-[1.625rem];
      }

    }

    &.s {

      @apply text-sm h-8 min-h-8;

      &.box {
        @apply w-8 min-w-8;
      }

      &.badge {
        @apply w-8 min-w-8;
      }

    }

    &.m {

      @apply h-10 min-h-10;

      &.box {
        @apply w-10 min-w-10;
      }

      &.badge {
        @apply w-10 min-w-10;
      }

    }

    &.l {
      
      @apply h-12 min-h-12;

      &.box {
        @apply w-12 min-w-12;
      }

      &.badge {
        @apply w-12 min-w-12;
      }

    }

    &.primary {

      @apply bg-button-primary text-text-on-color;

      &:hover {
        @apply bg-button-primary-hover;
      }

      &:active {
        @apply bg-button-primary-active;
      }

      /* Danger modifier for the primary mode */

      &.danger {

        @apply bg-button-danger-primary;

        &:hover {
          @apply bg-button-danger-hover;
        }

        &:active {
          @apply bg-button-danger-active;
        }

      }

      /* Success modifier for the primary mode */

      &.success {

        @apply bg-button-success-primary;

        &:hover {
          @apply bg-button-success-hover;
        }

        &:active {
          @apply bg-button-success-active;
        }

      }

      /* Neutral modifier for the primary mode */

      &.neutral {

        @apply bg-button-neutral-primary;

        &:hover {
          @apply bg-button-neutral-hover;
        }

        &:active {
          @apply bg-button-neutral-active;
        }

      }

      &:disabled {
        @apply bg-button-disabled text-text-on-color-disabled cursor-default; 
      }

    }

    &.secondary {

      @apply bg-button-secondary text-text-on-color;

      &:hover {
        @apply bg-button-secondary-hover;
      }

      &:active {
        @apply bg-button-secondary-active;
      }

      /* Danger modifier for the secondary mode */

      &.danger {

        @apply bg-transparent border border-button-danger-secondary text-button-danger-secondary;

        &:hover {
          @apply bg-button-danger-hover text-text-on-color border-button-danger-hover;
        }

        &:active {
          @apply bg-button-danger-active text-text-on-color border-button-danger-active;
        }

      }

      /* Success modifier for the secondary mode */

      &.success {

        @apply bg-transparent border border-button-success-secondary text-button-success-secondary;

        &:hover {
          @apply bg-button-success-hover text-text-on-color border-button-success-hover;
        }

        &:active {
          @apply bg-button-success-active text-text-on-color border-button-success-active;
        }

      }

      /* Neutral modifier for the secondary mode */

      &.neutral {

        @apply bg-transparent border border-button-neutral-secondary text-button-neutral-secondary;

        &:hover {
          @apply bg-button-neutral-hover text-text-on-color border-button-neutral-hover;
        }

        &:active {
          @apply bg-button-neutral-active text-text-on-color border-button-neutral-active;
        }

      }

      &:disabled {
        @apply bg-button-disabled text-text-on-color-disabled cursor-default; 
      }

    }

    &.tertiary {

      @apply border border-button-tertiary text-button-tertiary;

      &:hover {
        @apply bg-button-tertiary-hover text-text-inverse border-button-tertiary-hover;
      }

      &:active {
        @apply bg-button-tertiary-active text-text-inverse border-button-tertiary-active;
      }

      /* Danger modifier for the tertiary mode */

      &.danger {

        @apply border border-button-danger-secondary text-button-danger-secondary;

        &:hover {
          @apply bg-button-danger-hover text-text-on-color border-button-danger-hover;
        }

        &:active {
          @apply bg-button-danger-active text-text-on-color border-button-danger-active;
        }

      }

      /* Success modifier for the tertiary mode */

      &.success {

        @apply border border-button-success-secondary text-button-success-secondary;

        &:hover {
          @apply bg-button-success-hover text-text-on-color border-button-success-hover;
        }

        &:active {
          @apply bg-button-success-active text-text-on-color border-button-success-active;
        }

      }

      /* Neutral modifier for the tertiary mode */

      &.neutral {

        @apply border border-button-neutral-secondary text-button-neutral-secondary;

        &:hover {
          @apply bg-button-neutral-hover text-text-on-color border-button-neutral-hover;
        }

        &:active {
          @apply bg-button-neutral-active text-text-on-color border-button-neutral-active;
        }

      }

      &:disabled {

        @apply border border-button-disabled text-button-disabled cursor-default; 

        &:hover {
          @apply bg-transparent;
        }

      }

    }

    &.ghost {

      @apply text-link-primary;

      &:hover {
        @apply bg-background-hover text-link-primary-hover;
      }

      &:active {
        @apply bg-background-active text-link-primary-hover;
      }

      /* Danger modifier for the ghost mode */

      &.danger {

        @apply text-button-danger-secondary;

        &:hover {
          @apply bg-button-danger-hover text-text-on-color;
        }

        &:active {
          @apply bg-button-danger-active text-text-on-color;
        }

      }

      /* Success modifier for the ghost mode */

      &.success {

        @apply text-button-success-secondary;

        &:hover {
          @apply bg-button-success-hover text-text-on-color;
        }

        &:active {
          @apply bg-button-success-active text-text-on-color;
        }

      }

      /* Neutral modifier for the ghost mode */

      &.neutral {

        @apply text-button-neutral-secondary;

        &:hover {
          @apply bg-button-neutral-hover text-text-on-color;
        }

        &:active {
          @apply bg-button-neutral-active text-text-on-color;
        }

      }

      &:disabled {

        @apply text-button-disabled cursor-default; 

        &:hover {
          @apply bg-transparent text-button-disabled;
        }

      }

    }

    &.naked {

      @apply text-text-primary;

      &.high-contrast {
        @apply text-text-on-color;
      }

      &:hover {
        @apply bg-background-hover;
      }

      &:active {
        @apply bg-background-active;
      }

      /* Danger modifier for the naked mode */

      &.danger {

        @apply text-button-danger-secondary;

        &:hover {
          @apply bg-button-danger-hover text-text-on-color;
        }

        &:active {
          @apply bg-button-danger-active text-text-on-color;
        }

      }

      /* Success modifier for the naked mode */

      &.success {

        @apply text-button-success-secondary;

        &:hover {
          @apply bg-button-success-hover text-text-on-color;
        }

        &:active {
          @apply bg-button-success-active text-text-on-color;
        }

      }

      /* Neutral modifier for the naked mode */

      &.neutral {

        @apply text-button-neutral-secondary;

        &:hover {
          @apply bg-button-neutral-hover text-text-on-color;
        }

        &:active {
          @apply bg-button-neutral-active text-text-on-color;
        }

      }

      &:disabled {

        @apply text-button-disabled cursor-default; 

        &:hover {
          @apply bg-transparent text-button-disabled;
        }

      }

    }

    &.pill, &.badge {
      @apply rounded-[0.156rem];
    }

    &:focus-visible {
      @apply outline-2 outline-offset-1 outline-focus;
    }

  }

  .button.inverse {

    &.primary {

      @apply bg-button-primary-inverse text-text-on-color-inverse;

      &:hover {
        @apply bg-button-primary-hover-inverse;
      }

      &:active {
        @apply bg-button-primary-active-inverse;
      }

      /* Danger modifier for the primary mode */

      &.danger {

        @apply bg-button-danger-primary-inverse;

        &:hover {
          @apply bg-button-danger-hover-inverse;
        }

        &:active {
          @apply bg-button-danger-active-inverse;
        }

      }

      /* Success modifier for the primary mode */

      &.success {

        @apply bg-button-success-primary-inverse;

        &:hover {
          @apply bg-button-success-hover-inverse;
        }

        &:active {
          @apply bg-button-success-active-inverse;
        }

      }

      /* Neutral modifier for the primary mode */

      &.neutral {

        @apply bg-button-neutral-primary-inverse;

        &:hover {
          @apply bg-button-neutral-hover-inverse;
        }

        &:active {
          @apply bg-button-neutral-active-inverse;
        }

      }

      &:disabled {
        @apply bg-button-disabled-inverse text-text-on-color-disabled-inverse; 
      }

    }

    &.secondary {

      @apply bg-button-secondary-inverse text-text-on-color-inverse;

      &:hover {
        @apply bg-button-secondary-hover-inverse;
      }

      &:active {
        @apply bg-button-secondary-active-inverse;
      }

      /* Danger modifier for the secondary mode */

      &.danger {

        @apply border-button-danger-secondary-inverse text-button-danger-secondary-inverse;

        &:hover {
          @apply bg-button-danger-hover-inverse text-text-on-color-inverse border-button-danger-hover-inverse;
        }

        &:active {
          @apply bg-button-danger-active-inverse text-text-on-color-inverse border-button-danger-active-inverse;
        }

      }

      /* Success modifier for the secondary mode */

      &.success {

        @apply border-button-success-secondary-inverse text-button-success-secondary-inverse;

        &:hover {
          @apply bg-button-success-hover-inverse text-text-on-color-inverse border-button-success-hover-inverse;
        }

        &:active {
          @apply bg-button-success-active-inverse text-text-on-color-inverse border-button-success-active-inverse;
        }

      }

      /* Neutral modifier for the secondary mode */

      &.neutral {

        @apply border border-button-neutral-secondary-inverse text-button-neutral-secondary-inverse;

        &:hover {
          @apply bg-button-neutral-hover-inverse text-text-on-color-inverse border-button-neutral-hover-inverse;
        }

        &:active {
          @apply bg-button-neutral-active-inverse text-text-on-color-inverse border-button-neutral-active-inverse;
        }

      }

      &:disabled {
        @apply bg-button-disabled text-text-on-color-disabled-inverse;
      }

    }

    &.tertiary {

      @apply border-button-tertiary-inverse text-button-tertiary-inverse;

      &:hover {
        @apply bg-button-tertiary-hover-inverse text-text-inverse border-button-tertiary-hover-inverse;
      }

      &:active {
        @apply bg-button-tertiary-active-inverse text-text-inverse border-button-tertiary-active-inverse;
      }

      /* Danger modifier for the tertiary mode */

      &.danger {

        @apply border-button-danger-secondary-inverse text-button-danger-secondary-inverse;

        &:hover {
          @apply bg-button-danger-hover-inverse text-text-on-color-inverse border-button-danger-hover-inverse;
        }

        &:active {
          @apply bg-button-danger-active-inverse text-text-on-color-inverse border-button-danger-active-inverse;
        }

      }

      /* Success modifier for the tertiary mode */

      &.success {

        @apply border-button-success-secondary-inverse text-button-success-secondary-inverse;

        &:hover {
          @apply bg-button-success-hover-inverse text-text-on-color-inverse border-button-success-hover-inverse;
        }

        &:active {
          @apply bg-button-success-active-inverse text-text-on-color-inverse border-button-success-active-inverse;
        }

      }

      /* Neutral modifier for the tertiary mode */

      &.neutral {

        @apply border-button-neutral-secondary-inverse text-button-neutral-secondary-inverse;

        &:hover {
          @apply bg-button-neutral-hover-inverse text-text-on-color-inverse border-button-neutral-hover-inverse;
        }

        &:active {
          @apply bg-button-neutral-active-inverse text-text-on-color-inverse border-button-neutral-active-inverse;
        }

      }

      &:disabled {

        @apply border-button-disabled-inverse text-button-disabled-inverse;

      }

    }

    &.ghost {

      @apply text-link-primary-inverse;

      &:hover {
        @apply bg-background-hover-inverse text-link-primary-hover-inverse;
      }

      &:active {
        @apply bg-background-active-inverse text-link-primary-hover-inverse;
      }

      /* Danger modifier for the ghost mode */

      &.danger {

        @apply text-button-danger-secondary-inverse;

        &:hover {
          @apply bg-button-danger-hover-inverse text-text-on-color-inverse;
        }

        &:active {
          @apply bg-button-danger-active-inverse text-text-on-color-inverse;
        }

      }

      /* Success modifier for the ghost mode */

      &.success {

        @apply text-button-success-secondary-inverse;

        &:hover {
          @apply bg-button-success-hover-inverse text-text-on-color-inverse;
        }

        &:active {
          @apply bg-button-success-active-inverse text-text-on-color-inverse;
        }

      }

      /* Neutral modifier for the ghost mode */

      &.neutral {

        @apply text-button-neutral-secondary-inverse;

        &:hover {
          @apply bg-button-neutral-hover-inverse text-text-on-color-inverse;
        }

        &:active {
          @apply bg-button-neutral-active-inverse text-text-on-color-inverse;
        }

      }

      &:disabled {

        @apply text-button-disabled-inverse;

        &:hover {
          @apply text-button-disabled-inverse;
        }

      }

    }

    &.naked {

      @apply text-text-primary-inverse;

      &:hover {
        @apply bg-background-hover-inverse;
      }

      &:active {
        @apply bg-background-active-inverse;
      }

      /* Danger modifier for the ghost mode */

      &.danger {

        @apply text-button-danger-secondary-inverse;

        &:hover {
          @apply bg-button-danger-hover-inverse text-text-on-color-inverse;
        }

        &:active {
          @apply bg-button-danger-active-inverse text-text-on-color-inverse;
        }

      }

      /* Success modifier for the ghost mode */

      &.success {

        @apply text-button-success-secondary-inverse;

        &:hover {
          @apply bg-button-success-hover-inverse text-text-on-color-inverse;
        }

        &:active {
          @apply bg-button-success-active-inverse text-text-on-color-inverse;
        }

      }

      /* Neutral modifier for the ghost mode */

      &.neutral {

        @apply text-button-neutral-secondary-inverse;

        &:hover {
          @apply bg-button-neutral-hover-inverse text-text-on-color-inverse;
        }

        &:active {
          @apply bg-button-neutral-active-inverse text-text-on-color-inverse;
        }

      }

      &:disabled {

        @apply text-button-disabled-inverse;

        &:hover {
          @apply text-button-disabled-inverse;
        }

      }

    }

  }

}
